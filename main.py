"""
Main entry point for the Kobo Discord Bot.
"""

import os
import logging
from dotenv import load_dotenv

from scraper import KoboScraper

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main function to run the bot."""
    logger.info("Starting Kobo Discord Bot...")
    
    # Get configuration from environment variables
    kobo_blog_url = os.getenv('KOBO_BLOG_URL', 'https://www.kobo.com/zh/blog')
    discord_token = os.getenv('DISCORD_TOKEN')
    discord_channel_id = os.getenv('DISCORD_CHANNEL_ID')
    
    # For now, just test the scraper functionality
    # Discord integration will be added later when credentials are provided
    
    logger.info("Testing scraper functionality...")
    scraper = KoboScraper(kobo_blog_url)
    
    try:
        post = scraper.get_latest_available_post()
        
        if post:
            logger.info(f"Successfully scraped post: {post.title}")
            logger.info(f"Found {len(post.books)} book recommendations")
            
            # Print the results for now
            print("\n" + "="*60)
            print(f"POST: {post.title}")
            print(f"URL: {post.url}")
            if post.date:
                print(f"Date: {post.date}")
            print("="*60)
            
            for i, book in enumerate(post.books, 1):
                print(f"\n{i}. 📚 {book.title}")
                if book.author:
                    print(f"   👤 Author: {book.author}")
                if book.price:
                    print(f"   💰 Price: {book.price}")
                if book.description:
                    print(f"   📝 Description: {book.description}")
                if book.link:
                    print(f"   🔗 Link: {book.link}")
            
            print("\n" + "="*60)
            
            # TODO: When Discord credentials are provided, send this to Discord
            if discord_token and discord_channel_id:
                logger.info("Discord credentials found - would send to Discord here")
                # Discord integration will be implemented later
            else:
                logger.info("Discord credentials not provided - skipping Discord integration")
                
        else:
            logger.warning("No book post found")
            
    except Exception as e:
        logger.error(f"Error running scraper: {e}")
        raise


if __name__ == "__main__":
    main()
