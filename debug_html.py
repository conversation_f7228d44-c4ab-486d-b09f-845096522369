"""
Debug script to examine the HTML structure of the Kobo blog post.
"""

import requests
from bs4 import BeautifulSoup
from scraper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def debug_post_structure():
    """Debug the HTML structure of the latest post."""
    scraper = KoboScraper()

    # Use the new direct URL construction
    from datetime import datetime
    current_year = datetime.now().year
    current_week = datetime.now().isocalendar()[1]
    post_url = f"{scraper.base_url}/weekly-dd99-{current_year}-w{current_week:02d}"
    print(f"Using direct URL: {post_url}")
    
    # Get the post content
    print("Fetching post content...")
    response = scraper.session.get(post_url)
    post_soup = BeautifulSoup(response.content, 'html.parser')
    
    # Save the HTML for inspection
    with open('post_content.html', 'w', encoding='utf-8') as f:
        f.write(post_soup.prettify())
    print("Saved post content to post_content.html")
    
    # Look for potential content areas
    print("\n=== CONTENT AREAS ===")
    content_selectors = [
        '.post-content',
        '.entry-content', 
        '.content',
        'article',
        '.main-content',
        '.blog-post-content',
        '.post-body',
    ]
    
    for selector in content_selectors:
        elements = post_soup.select(selector)
        if elements:
            print(f"Found {len(elements)} elements with selector: {selector}")
            for i, elem in enumerate(elements):
                text_preview = elem.get_text(strip=True)[:200]
                print(f"  {i+1}. {text_preview}...")
    
    # Look for lists and paragraphs
    print("\n=== LISTS ===")
    lists = post_soup.find_all(['ul', 'ol'])
    print(f"Found {len(lists)} lists")
    for i, lst in enumerate(lists[:3]):  # Show first 3 lists
        items = lst.find_all('li')
        print(f"List {i+1}: {len(items)} items")
        for j, item in enumerate(items[:2]):  # Show first 2 items
            text = item.get_text(strip=True)[:100]
            print(f"  Item {j+1}: {text}...")
    
    print("\n=== PARAGRAPHS ===")
    paragraphs = post_soup.find_all('p')
    print(f"Found {len(paragraphs)} paragraphs")
    for i, p in enumerate(paragraphs[:5]):  # Show first 5 paragraphs
        text = p.get_text(strip=True)
        if len(text) > 20:  # Only show substantial paragraphs
            print(f"P{i+1}: {text[:150]}...")
    
    # Look for book-like patterns
    print("\n=== BOOK PATTERNS ===")
    all_text = post_soup.get_text()
    
    import re
    # Look for Chinese book title patterns
    book_titles = re.findall(r'《([^》]+)》', all_text)
    if book_titles:
        print(f"Found {len(book_titles)} potential book titles:")
        for title in book_titles[:5]:
            print(f"  - {title}")
    
    # Look for author patterns
    authors = re.findall(r'作者[：:]([^\n\r，,]+)', all_text)
    if authors:
        print(f"Found {len(authors)} potential authors:")
        for author in authors[:5]:
            print(f"  - {author.strip()}")
    
    # Look for price patterns
    prices = re.findall(r'NT\$\s*[0-9,]+', all_text)
    if prices:
        print(f"Found {len(prices)} potential prices:")
        for price in prices[:5]:
            print(f"  - {price}")

if __name__ == "__main__":
    debug_post_structure()
