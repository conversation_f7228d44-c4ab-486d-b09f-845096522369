"""
Kobo blog scraper for extracting book recommendations.
"""

import logging
import re
from typing import Dict, List, Optional
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
from datetime import datetime

import requests
from bs4 import BeautifulSoup


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class BookRecommendation:
    """Data class for book recommendation information."""
    title: str
    author: Optional[str] = None
    description: Optional[str] = None
    price: Optional[str] = None
    link: Optional[str] = None


@dataclass
class BlogPost:
    """Data class for blog post information."""
    title: str
    url: str
    date: Optional[str] = None
    books: List[BookRecommendation] = None
    
    def __post_init__(self):
        if self.books is None:
            self.books = []


class KoboScraper:
    """Scraper for Kobo Taiwan blog book recommendations."""
    
    def __init__(self, base_url: str = "https://www.kobo.com/zh/blog"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_latest_book_post(self, week_offset: int = 0) -> Optional[BlogPost]:
        """
        Scrape the latest book recommendation post from Kobo blog.

        Args:
            week_offset: Offset from current week (0 = current week, -1 = last week, etc.)

        Returns:
            BlogPost object with book recommendations, or None if not found.
        """
        try:
            # Calculate the current week and construct URL directly
            current_year = datetime.now().year
            current_week = datetime.now().isocalendar()[1] + week_offset

            # Handle year boundary cases
            if current_week <= 0:
                current_year -= 1
                # Get the last week of previous year
                last_day_prev_year = datetime(current_year, 12, 31)
                current_week = last_day_prev_year.isocalendar()[1] + current_week
            elif current_week > 52:
                # Check if we need to go to next year
                try:
                    datetime.fromisocalendar(current_year, current_week, 1)
                except ValueError:
                    current_year += 1
                    current_week = current_week - 52

            post_url = f"{self.base_url}/weekly-dd99-{current_year}-w{current_week:02d}"
            logger.info(f"Constructed post URL for week {current_week} of {current_year}: {post_url}")

            return self._scrape_post_content(post_url)

        except Exception as e:
            logger.error(f"Unexpected error in get_latest_book_post: {e}")
            return None

    def get_latest_available_post(self) -> Optional[BlogPost]:
        """
        Get the latest available book post, trying current week and previous weeks.

        Returns:
            BlogPost object with book recommendations, or None if not found.
        """
        # Try current week and up to 3 previous weeks
        for week_offset in range(0, -4, -1):
            logger.info(f"Trying week offset {week_offset}")
            post = self.get_latest_book_post(week_offset)
            if post:
                return post

        logger.warning("No book post found in current or previous 3 weeks")
        return None
    

    
    def _scrape_post_content(self, post_url: str) -> Optional[BlogPost]:
        """
        Scrape the content of a specific blog post.
        
        Args:
            post_url: URL of the blog post to scrape
            
        Returns:
            BlogPost object with extracted information.
        """
        try:
            logger.info(f"Scraping post content: {post_url}")
            response = self.session.get(post_url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract post title
            title = self._extract_post_title(soup)
            
            # Extract post date
            date = self._extract_post_date(soup)
            
            # Extract book recommendations
            books = self._extract_book_recommendations(soup)
            
            return BlogPost(
                title=title,
                url=post_url,
                date=date,
                books=books
            )
            
        except requests.RequestException as e:
            logger.error(f"Error fetching post content: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in _scrape_post_content: {e}")
            return None
    
    def _extract_post_title(self, soup: BeautifulSoup) -> str:
        """Extract the post title from the page."""
        # Try different selectors for post title
        selectors = [
            'h1.post-title',
            'h1.entry-title', 
            '.post-header h1',
            'article h1',
            'h1',
        ]
        
        for selector in selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                return title_elem.get_text(strip=True)
        
        # Fallback to page title
        title_elem = soup.find('title')
        if title_elem:
            return title_elem.get_text(strip=True)
        
        return "Unknown Title"
    
    def _extract_post_date(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract the post date from the page."""
        # Try different selectors for post date
        selectors = [
            '.post-date',
            '.entry-date',
            '.published',
            'time',
            '.date',
        ]
        
        for selector in selectors:
            date_elem = soup.select_one(selector)
            if date_elem:
                # Try to get datetime attribute first
                date_str = date_elem.get('datetime') or date_elem.get_text(strip=True)
                if date_str:
                    return date_str
        
        return None
    
    def _extract_book_recommendations(self, soup: BeautifulSoup) -> List[BookRecommendation]:
        """
        Extract book recommendations from the post content.
        
        Args:
            soup: BeautifulSoup object of the post page
            
        Returns:
            List of BookRecommendation objects.
        """
        books = []
        
        # Look for the main content area
        content_selectors = [
            '.post-content',
            '.entry-content',
            '.content',
            'article',
            '.main-content',
        ]
        
        content = None
        for selector in content_selectors:
            content = soup.select_one(selector)
            if content:
                break
        
        if not content:
            content = soup  # Use entire page as fallback
        
        # Extract book information using various patterns
        books.extend(self._extract_books_from_lists(content))
        books.extend(self._extract_books_from_paragraphs(content))
        
        logger.info(f"Extracted {len(books)} book recommendations")
        return books
    
    def _extract_books_from_lists(self, content: BeautifulSoup) -> List[BookRecommendation]:
        """Extract books from list elements (ul, ol)."""
        books = []
        
        # Find all list items
        list_items = content.find_all(['li', 'div'])
        
        for item in list_items:
            text = item.get_text(strip=True)
            if self._looks_like_book_entry(text):
                book = self._parse_book_text(text, item)
                if book:
                    books.append(book)
        
        return books
    
    def _extract_books_from_paragraphs(self, content: BeautifulSoup) -> List[BookRecommendation]:
        """Extract books from paragraph elements."""
        books = []
        
        paragraphs = content.find_all('p')
        
        for p in paragraphs:
            text = p.get_text(strip=True)
            if self._looks_like_book_entry(text):
                book = self._parse_book_text(text, p)
                if book:
                    books.append(book)
        
        return books
    
    def _looks_like_book_entry(self, text: str) -> bool:
        """Check if text looks like a book entry."""
        if len(text) < 10:  # Too short
            return False
        
        # Look for patterns that suggest book information
        book_indicators = [
            r'作者[：:]',  # Author indicator
            r'價格[：:]',  # Price indicator  
            r'NT\$',      # Price in NT dollars
            r'《.*》',    # Book title in Chinese quotes
            r'".*"',      # Book title in quotes
            r'\..*著',    # Author pattern
        ]
        
        for pattern in book_indicators:
            if re.search(pattern, text):
                return True
        
        return False
    
    def _parse_book_text(self, text: str, element) -> Optional[BookRecommendation]:
        """
        Parse book information from text.
        
        Args:
            text: Text content to parse
            element: BeautifulSoup element for additional context
            
        Returns:
            BookRecommendation object or None.
        """
        # Extract title (look for content in quotes or specific patterns)
        title = self._extract_title_from_text(text)
        if not title:
            return None
        
        # Extract author
        author = self._extract_author_from_text(text)
        
        # Extract price
        price = self._extract_price_from_text(text)
        
        # Extract link if available
        link = self._extract_link_from_element(element)
        
        # Use remaining text as description
        description = self._extract_description_from_text(text, title, author, price)
        
        return BookRecommendation(
            title=title,
            author=author,
            description=description,
            price=price,
            link=link
        )
    
    def _extract_title_from_text(self, text: str) -> Optional[str]:
        """Extract book title from text."""
        # Try different title patterns
        patterns = [
            r'《([^》]+)》',  # Chinese book quotes
            r'"([^"]+)"',    # Regular quotes
            r'「([^」]+)」',  # Japanese quotes
            r'『([^』]+)』',  # Another quote style
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        # If no quotes found, try to extract from beginning of text
        # Remove common prefixes and take first meaningful part
        clean_text = re.sub(r'^[0-9]+[\.、\s]*', '', text)  # Remove numbering
        if len(clean_text) > 5:
            # Take first sentence or up to first colon/dash
            title_match = re.match(r'^([^：:—\-\n]+)', clean_text)
            if title_match:
                potential_title = title_match.group(1).strip()
                if len(potential_title) > 3:
                    return potential_title
        
        return None
    
    def _extract_author_from_text(self, text: str) -> Optional[str]:
        """Extract author from text."""
        patterns = [
            r'作者[：:]\s*([^\n\r，,]+)',
            r'著者[：:]\s*([^\n\r，,]+)', 
            r'([^，,\s]+)\s*著',
            r'作[：:]\s*([^\n\r，,]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                author = match.group(1).strip()
                # Clean up author name
                author = re.sub(r'[（(].*?[）)]', '', author)  # Remove parentheses
                return author.strip()
        
        return None
    
    def _extract_price_from_text(self, text: str) -> Optional[str]:
        """Extract price from text."""
        patterns = [
            r'NT\$\s*([0-9,]+)',
            r'價格[：:]\s*([0-9,]+)',
            r'售價[：:]\s*([0-9,]+)',
            r'\$\s*([0-9,]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(0).strip()
        
        return None
    
    def _extract_link_from_element(self, element) -> Optional[str]:
        """Extract link from BeautifulSoup element."""
        # Check if element itself is a link
        if element.name == 'a':
            return element.get('href')
        
        # Look for links within the element
        link = element.find('a')
        if link:
            href = link.get('href')
            if href:
                return urljoin(self.base_url, href)
        
        return None
    
    def _extract_description_from_text(self, text: str, title: str = None, 
                                     author: str = None, price: str = None) -> Optional[str]:
        """Extract description by removing title, author, and price from text."""
        description = text
        
        # Remove title, author, price from description
        if title:
            description = description.replace(f'《{title}》', '').replace(f'"{title}"', '')
        if author:
            description = re.sub(rf'{re.escape(author)}\s*著', '', description)
            description = re.sub(rf'作者[：:]\s*{re.escape(author)}', '', description)
        if price:
            description = description.replace(price, '')
        
        # Clean up description
        description = re.sub(r'^[0-9]+[\.、\s]*', '', description)  # Remove numbering
        description = re.sub(r'\s+', ' ', description)  # Normalize whitespace
        description = description.strip()
        
        return description if len(description) > 10 else None


def main():
    """Test the scraper functionality."""
    scraper = KoboScraper()
    post = scraper.get_latest_available_post()
    
    if post:
        print(f"Found post: {post.title}")
        print(f"URL: {post.url}")
        print(f"Date: {post.date}")
        print(f"Books found: {len(post.books)}")
        
        for i, book in enumerate(post.books, 1):
            print(f"\n{i}. {book.title}")
            if book.author:
                print(f"   Author: {book.author}")
            if book.price:
                print(f"   Price: {book.price}")
            if book.description:
                print(f"   Description: {book.description[:100]}...")
    else:
        print("No book post found")


if __name__ == "__main__":
    main()
