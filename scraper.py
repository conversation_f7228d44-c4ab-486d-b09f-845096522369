"""
Kobo blog scraper for extracting book recommendations.
"""

import logging
import re
from typing import Dict, List, Optional
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
from datetime import datetime

import requests
from bs4 import BeautifulSoup


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class BookRecommendation:
    """Data class for book recommendation information."""
    title: str
    author: Optional[str] = None
    description: Optional[str] = None
    price: Optional[str] = None
    link: Optional[str] = None


@dataclass
class BlogPost:
    """Data class for blog post information."""
    title: str
    url: str
    date: Optional[str] = None
    books: List[BookRecommendation] = None
    
    def __post_init__(self):
        if self.books is None:
            self.books = []


class KoboScraper:
    """Scraper for Kobo Taiwan blog book recommendations."""
    
    def __init__(self, base_url: str = "https://www.kobo.com/zh/blog"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_latest_book_post(self, week_offset: int = 0) -> Optional[BlogPost]:
        """
        Scrape the latest book recommendation post from Kobo blog.

        Args:
            week_offset: Offset from current week (0 = current week, -1 = last week, etc.)

        Returns:
            BlogPost object with book recommendations, or None if not found.
        """
        try:
            # Calculate the current week and construct URL directly
            current_year = datetime.now().year
            current_week = datetime.now().isocalendar()[1] + week_offset

            # Handle year boundary cases
            if current_week <= 0:
                current_year -= 1
                # Get the last week of previous year
                last_day_prev_year = datetime(current_year, 12, 31)
                current_week = last_day_prev_year.isocalendar()[1] + current_week
            elif current_week > 52:
                # Check if we need to go to next year
                try:
                    datetime.fromisocalendar(current_year, current_week, 1)
                except ValueError:
                    current_year += 1
                    current_week = current_week - 52

            post_url = f"{self.base_url}/weekly-dd99-{current_year}-w{current_week:02d}"
            logger.info(f"Constructed post URL for week {current_week} of {current_year}: {post_url}")

            return self._scrape_post_content(post_url)

        except Exception as e:
            logger.error(f"Unexpected error in get_latest_book_post: {e}")
            return None

    def get_latest_available_post(self) -> Optional[BlogPost]:
        """
        Get the latest available book post, trying current week and previous weeks.

        Returns:
            BlogPost object with book recommendations, or None if not found.
        """
        # Start from previous week since current week (37) might not be available yet
        # Try previous week and up to 3 weeks back
        for week_offset in range(-1, -5, -1):
            logger.info(f"Trying week offset {week_offset}")
            post = self.get_latest_book_post(week_offset)
            if post:
                return post

        logger.warning("No book post found in previous 4 weeks")
        return None
    

    
    def _scrape_post_content(self, post_url: str) -> Optional[BlogPost]:
        """
        Scrape the content of a specific blog post.
        
        Args:
            post_url: URL of the blog post to scrape
            
        Returns:
            BlogPost object with extracted information.
        """
        try:
            logger.info(f"Scraping post content: {post_url}")
            response = self.session.get(post_url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract post title
            title = self._extract_post_title(soup)
            
            # Extract post date
            date = self._extract_post_date(soup)
            
            # Extract book recommendations
            books = self._extract_book_recommendations(soup)
            
            return BlogPost(
                title=title,
                url=post_url,
                date=date,
                books=books
            )
            
        except requests.RequestException as e:
            logger.error(f"Error fetching post content: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in _scrape_post_content: {e}")
            return None
    
    def _extract_post_title(self, soup: BeautifulSoup) -> str:
        """Extract the post title from the page."""
        # Try different selectors for post title
        selectors = [
            'h1.post-title',
            'h1.entry-title', 
            '.post-header h1',
            'article h1',
            'h1',
        ]
        
        for selector in selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                return title_elem.get_text(strip=True)
        
        # Fallback to page title
        title_elem = soup.find('title')
        if title_elem:
            return title_elem.get_text(strip=True)
        
        return "Unknown Title"
    
    def _extract_post_date(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract the post date from the page."""
        # Try different selectors for post date
        selectors = [
            '.post-date',
            '.entry-date',
            '.published',
            'time',
            '.date',
        ]
        
        for selector in selectors:
            date_elem = soup.select_one(selector)
            if date_elem:
                # Try to get datetime attribute first
                date_str = date_elem.get('datetime') or date_elem.get_text(strip=True)
                if date_str:
                    return date_str
        
        return None
    
    def _extract_book_recommendations(self, soup: BeautifulSoup) -> List[BookRecommendation]:
        """
        Extract book recommendations from the post content.

        Args:
            soup: BeautifulSoup object of the post page

        Returns:
            List of BookRecommendation objects.
        """
        books = []

        # Look for book titles in the specific Kobo blog structure
        # Books are in <span class="title"> elements
        title_elements = soup.find_all('span', class_='title')

        for title_elem in title_elements:
            title_text = title_elem.get_text(strip=True)

            # Skip if this doesn't look like a book title
            if not self._looks_like_book_title(title_text):
                continue

            # Extract title (remove Chinese quotes if present)
            title = self._clean_book_title(title_text)

            # Look for author in the same parent element
            author = None
            parent = title_elem.parent
            if parent:
                author_elem = parent.find('span', class_='author')
                if author_elem:
                    author_text = author_elem.get_text(strip=True)
                    author = self._clean_author_text(author_text)

            # Look for price (if available)
            price = None
            if parent:
                price_elem = parent.find('span', class_='price')
                if price_elem:
                    price = price_elem.get_text(strip=True)

            # Look for link
            link = None
            if parent:
                link_elem = parent.find('a')
                if link_elem:
                    href = link_elem.get('href')
                    if href:
                        link = urljoin(self.base_url, href)

            # Create book recommendation
            book = BookRecommendation(
                title=title,
                author=author,
                price=price,
                link=link
            )
            books.append(book)

        logger.info(f"Extracted {len(books)} book recommendations")
        return books

    def _looks_like_book_title(self, text: str) -> bool:
        """Check if text looks like a book title."""
        if len(text) < 3:
            return False

        # Look for book title patterns
        book_patterns = [
            r'《.*》',  # Chinese book quotes
            r'".*"',    # Regular quotes
            r'「.*」',  # Japanese quotes
        ]

        for pattern in book_patterns:
            if re.search(pattern, text):
                return True

        # If it's reasonably long and doesn't contain common non-book indicators
        if len(text) > 5 and not any(indicator in text.lower() for indicator in ['http', 'www', '@', 'kobo']):
            return True

        return False

    def _clean_book_title(self, title_text: str) -> str:
        """Clean and extract book title from text."""
        # Remove Chinese quotes
        title = re.sub(r'^《(.*)》$', r'\1', title_text)
        # Remove regular quotes
        title = re.sub(r'^"(.*)"$', r'\1', title)
        # Remove Japanese quotes
        title = re.sub(r'^「(.*)」$', r'\1', title)
        return title.strip()

    def _clean_author_text(self, author_text: str) -> str:
        """Clean author text by removing common prefixes and suffixes."""
        # Remove common prefixes like "由 " and suffixes like "◎著"
        author = re.sub(r'^由\s*', '', author_text)
        author = re.sub(r'◎著$', '', author)
        author = re.sub(r'著$', '', author)
        # Remove parentheses content
        author = re.sub(r'[（(].*?[）)]', '', author)
        return author.strip()
    





def main():
    """Test the scraper functionality."""
    scraper = KoboScraper()
    post = scraper.get_latest_available_post()
    
    if post:
        print(f"Found post: {post.title}")
        print(f"URL: {post.url}")
        print(f"Date: {post.date}")
        print(f"Books found: {len(post.books)}")
        
        for i, book in enumerate(post.books, 1):
            print(f"\n{i}. {book.title}")
            if book.author:
                print(f"   Author: {book.author}")
            if book.price:
                print(f"   Price: {book.price}")
            if book.description:
                print(f"   Description: {book.description[:100]}...")
    else:
        print("No book post found")


if __name__ == "__main__":
    main()
