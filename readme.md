# README.md

# Kobo "Book of the Day" Discord Bot

This project is an automated Discord bot that scrapes the **"Book of the Day"** posts from the Kobo Taiwan blog and sends the curated book recommendations directly to a registered Discord server channel.

***

## Features

- Automatically scrapes the Kobo blog at [https://www.kobo.com/zh/blog](https://www.kobo.com/zh/blog)
- Detects posts starting with the title prefix **"【一週99書單】"** which contain weekly book recommendations
- Extracts book titles, descriptions, authors, and related details
- Posts curated content neatly formatted to a designated Discord channel
- Supports scheduled daily scraping with configurable frequency
- Easy configuration with environment variables

***

## Getting Started

### Prerequisites

- Python 3.10+
- Discord account with permissions to create a bot and get bot token
- A Discord server where you have permission to add the bot and send messages

### Installation

1. Clone this repository:
   ```bash
   git clone https://github.com/yourusername/kobo-discord-bot.git
   cd kobo-discord-bot
   ```

2. Create and activate a virtual environment (optional but recommended):
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # Linux/macOS
   venv\Scripts\activate     # Windows
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Configuration

Create a `.env` file in the project root directory with the following environment variables:

```ini
DISCORD_TOKEN=your_discord_bot_token_here
DISCORD_CHANNEL_ID=your_target_channel_id_here
KOBO_BLOG_URL=https://www.kobo.com/zh/blog
SCRAPE_FREQUENCY_CRON="0 9 * * *"  # Optional cron schedule for scraping (default: daily 9 AM UTC)
```

- `DISCORD_TOKEN`: Your Discord Bot Token from Discord Developer Portal.
- `DISCORD_CHANNEL_ID`: The ID of the Discord channel where messages will be posted.
- `KOBO_BLOG_URL`: Kobo blog main page URL.
- `SCRAPE_FREQUENCY_CRON`: (Optional) Cron format string for scheduling the scraper.

### Running the Bot Locally

```bash
python main.py
```

The bot will connect to Discord, perform the initial scrape for the latest "Book of the Day" post, and send the information to the configured Discord channel. It will continue to run and scrape the site according to the schedule.

***

## How It Works

1. The scraper fetches the main Kobo blog page and looks for the latest post with a title starting with `"【一週99書單】"`.
2. It extracts the post details including the book list and descriptions.
3. The bot formats the book information into a clean, readable Discord message embed.
4. The formatted message is sent to the specified Discord channel.
5. The job runs daily or on a custom schedule set in `SCRAPE_FREQUENCY_CRON`.

***

## Dependencies

- `requests` - For HTTP requests
- `beautifulsoup4` - For parsing HTML content of the blog
- `discord.py` - For Discord bot interaction
- `python-dotenv` - For environment variable management
- `apscheduler` - For scheduling periodic scraping jobs

***

## Development

- The bot code lives mainly in `main.py` and `scraper.py`.
- Use Cogs or modularize Discord commands if you want to extend functionalities.
- Logging is configured to show scraping and Discord bot status.

***

## Error Handling

- If the blog page cannot be reached or parsing fails, the bot logs the error and posts a notification message in Discord.
- Rate limits from Discord API are respected with simple retry mechanisms.

***

## Contributing

Contributions and suggestions are welcome! Please open issues or submit pull requests.

***

## License

This project is licensed under the MIT License.

***

## Contact

For support or questions, please open an issue on the GitHub repo or reach out to the maintainer.

***

This bot makes it easy to keep your Discord community updated with fresh weekly book recommendations from Kobo Taiwan’s official blog digest.

***

Would you like a sample code snippet or help setting up the Discord bot next?

[1](https://github.com/kkrypt0nn/Python-Discord-Bot-Template)
[2](https://www.youtube.com/watch?v=YD_N6Ffoojw)
[3](https://www.onlydust.com/repositories/Robin5605/discord-bot-template)
[4](https://glitch.com/~discord-python-bot-template)
[5](https://www.npmjs.com/package/@mattythedev01/easydiscord)
[6](https://dev.to/rohan_sharma/heres-how-i-created-a-real-time-discord-badge-for-github-readme-4adg)
[7](https://www.onlydust.com/repositories/DenverCoder1/discord-bot-template)
[8](https://www.reddit.com/r/Python/comments/ie1mb6/discord_bot_template/)
[9](https://stackoverflow.com/questions/11509830/how-to-add-color-to-githubs-readme-md-file)
[10](https://discordpy.readthedocs.io)