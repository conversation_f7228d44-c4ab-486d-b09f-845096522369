# README.md

# Kobo "Book of the Day" Discord Bot

This project is an automated Discord bot that scrapes the **"Book of the Day"** posts from the Kobo Taiwan blog and sends the curated book recommendations directly to a registered Discord server channel.

***

## Features

- Automatically scrapes the Kobo blog at [https://www.kobo.com/zh/blog](https://www.kobo.com/zh/blog)
- Detects posts starting with the title prefix **"【一週99書單】"** which contain weekly book recommendations
- Extracts book titles, descriptions, authors, and related details
- Posts curated content neatly formatted to a designated Discord channel

- Easy configuration with environment variables

***

## Getting Started

### Prerequisites

- Python 3.10+
- Discord account with permissions to create a bot and get bot token
- A Discord server where you have permission to add the bot and send messages

### Installation

1. Clone this repository:
   ```bash
   git clone https://github.com/yourusername/kobo-discord-bot.git
   cd kobo-discord-bot
   ```

2. Install dependencies using uv:
   ```bash
   # Install uv if you haven't already
   curl -LsSf https://astral.sh/uv/install.sh | sh

   # Install dependencies
   uv sync
   ```

### Configuration

Create a `.env` file in the project root directory with the following environment variables:

```ini
DISCORD_TOKEN=your_discord_bot_token_here
DISCORD_CHANNEL_ID=your_target_channel_id_here
KOBO_BLOG_URL=https://www.kobo.com/zh/blog
```

- `DISCORD_TOKEN`: Your Discord Bot Token from Discord Developer Portal.
- `DISCORD_CHANNEL_ID`: The ID of the Discord channel where messages will be posted.
- `KOBO_BLOG_URL`: Kobo blog main page URL.

### Running the Bot Locally

```bash
uv run main.py
```

The bot will connect to Discord, perform the initial scrape for the latest "Book of the Day" post, and send the information to the configured Discord channel.

***

## How It Works

1. The scraper fetches the main Kobo blog page and looks for the latest post with a title starting with `"【一週99書單】"`.
2. It extracts the post details including the book list and descriptions.
3. The bot formats the book information into a clean, readable Discord message embed.
4. The formatted message is sent to the specified Discord channel.


***

## Dependencies

- `requests` - For HTTP requests
- `beautifulsoup4` - For parsing HTML content of the blog
- `discord.py` - For Discord bot interaction
- `python-dotenv` - For environment variable management


***

## License

This project is licensed under the MIT License.
