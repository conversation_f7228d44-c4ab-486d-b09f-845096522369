#!/usr/bin/env python3
"""
Demo script to show the Kobo scraper functionality.
"""

from scraper import KoboScraper
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def main():
    """Demo the scraper functionality."""
    print("🤖 Kobo Book Scraper Demo")
    print("=" * 50)
    
    # Create scraper instance
    scraper = KoboScraper()
    
    try:
        # Get the latest available post
        print("📡 Fetching latest book recommendations...")
        post = scraper.get_latest_available_post()
        
        if post:
            print(f"✅ Successfully found post!")
            print()
            print(f"📖 Title: {post.title}")
            print(f"🔗 URL: {post.url}")
            if post.date:
                print(f"📅 Date: {post.date}")
            print(f"📚 Books found: {len(post.books)}")
            print()
            print("📋 Book List:")
            print("-" * 50)
            
            for i, book in enumerate(post.books, 1):
                print(f"{i:2d}. 📚 {book.title}")
                if book.author:
                    print(f"     👤 {book.author}")
                if book.price:
                    print(f"     💰 {book.price}")
                if book.link:
                    print(f"     🔗 {book.link}")
                print()
            
            print("=" * 50)
            print("✨ Demo completed successfully!")
            
        else:
            print("❌ No book post found")
            
    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
